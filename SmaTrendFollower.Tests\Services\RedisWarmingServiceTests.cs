using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Moq;
using Xunit;
using FluentAssertions;
using SmaTrendFollower.Services;
using SmaTrendFollower.Data;
using SmaTrendFollower.Models;
using StackExchange.Redis;

namespace SmaTrendFollower.Tests.Services;

public class RedisWarmingServiceTests : IDisposable
{
    private readonly Mock<ILogger<RedisWarmingService>> _mockLogger;
    private readonly Mock<IUniverseProvider> _mockUniverseProvider;
    private readonly StockBarCacheDbContext _dbContext;
    private readonly IConfiguration _configuration;
    private readonly RedisWarmingService _service;

    public RedisWarmingServiceTests()
    {
        _mockLogger = new Mock<ILogger<RedisWarmingService>>();
        _mockUniverseProvider = new Mock<IUniverseProvider>();

        // Setup in-memory database
        var options = new DbContextOptionsBuilder<StockBarCacheDbContext>()
            .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
            .Options;
        _dbContext = new StockBarCacheDbContext(options);

        // Setup configuration for Redis
        var configData = new Dictionary<string, string>
        {
            {"REDIS_URL", "localhost:6379"},
            {"REDIS_DATABASE", "0"}
        };
        _configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(configData!)
            .Build();

        // Setup universe provider mock
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync())
            .ReturnsAsync(new[] { "AAPL", "MSFT", "GOOGL", "SPY" });

        _service = new RedisWarmingService(
            _configuration,
            _dbContext,
            _mockUniverseProvider.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task WarmCacheAsync_ShouldCompleteSuccessfully_WhenValidConfiguration()
    {
        // Arrange
        await SeedTestDataAsync();

        // Act
        var act = async () => await _service.WarmCacheAsync();

        // Assert
        await act.Should().NotThrowAsync();
        
        // Verify logging
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Starting Redis cache warming")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Redis cache warming completed successfully")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task WarmCacheAsync_ShouldLoadTrailingStops_WhenDataExists()
    {
        // Arrange
        var testSymbol = "AAPL";
        var testDate = DateTime.UtcNow.Date;
        
        var trailingStop = new TrailingStopRecord
        {
            Symbol = testSymbol,
            Date = testDate,
            StopPrice = 150.00m,
            EntryPrice = 160.00m,
            Atr = 2.50m,
            HighWaterMark = 165.00m,
            Quantity = 100m,
            EntryDate = testDate.AddDays(-5),
            IsActive = true,
            CreatedAt = DateTime.UtcNow
        };

        _dbContext.TrailingStops.Add(trailingStop);
        await _dbContext.SaveChangesAsync();

        // Act
        await _service.WarmCacheAsync();

        // Assert
        var latestStop = await _dbContext.GetLatestTrailingStopAsync(testSymbol);
        latestStop.Should().NotBeNull();
        latestStop!.Symbol.Should().Be(testSymbol);
        latestStop.StopPrice.Should().Be(150.00m);
    }

    [Fact]
    public async Task PersistRedisStateAsync_ShouldCompleteSuccessfully()
    {
        // Arrange
        await SeedTestDataAsync();

        // Act
        var act = async () => await _service.PersistRedisStateAsync();

        // Assert
        await act.Should().NotThrowAsync();
        
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Persisting Redis state to SQLite")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task ClearCacheAsync_ShouldCompleteSuccessfully()
    {
        // Arrange
        await SeedTestDataAsync();

        // Act
        var act = async () => await _service.ClearCacheAsync();

        // Assert
        await act.Should().NotThrowAsync();
        
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Clearing Redis cache")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task WarmCacheAsync_ShouldHandleEmptyDatabase_Gracefully()
    {
        // Arrange - no data in database

        // Act
        var act = async () => await _service.WarmCacheAsync();

        // Assert
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public async Task WarmCacheAsync_ShouldHandleUniverseProviderFailure_Gracefully()
    {
        // Arrange
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync())
            .ThrowsAsync(new InvalidOperationException("Universe provider failed"));

        // Act
        var act = async () => await _service.WarmCacheAsync();

        // Assert
        await act.Should().NotThrowAsync();
        
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Error loading universe symbols")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public void RedisTrailingStop_GetRedisKey_ShouldReturnCorrectFormat()
    {
        // Arrange
        var symbol = "AAPL";

        // Act
        var key = RedisTrailingStop.GetRedisKey(symbol);

        // Assert
        key.Should().Be("stop:AAPL");
    }

    [Fact]
    public void RedisSignalFlag_GetRedisKey_ShouldReturnCorrectFormat()
    {
        // Arrange
        var symbol = "AAPL";
        var date = new DateTime(2024, 6, 20);

        // Act
        var key = RedisSignalFlag.GetRedisKey(symbol, date);

        // Assert
        key.Should().Be("signal:AAPL:20240620");
    }

    [Fact]
    public void RedisThrottleFlag_GetRedisKey_ShouldReturnCorrectFormat()
    {
        // Arrange
        var symbol = "AAPL";
        var date = new DateTime(2024, 6, 20);

        // Act
        var key = RedisThrottleFlag.GetRedisKey(symbol, date);

        // Assert
        key.Should().Be("block:AAPL:20240620");
    }

    [Fact]
    public void RedisTrailingStop_JsonSerialization_ShouldRoundTrip()
    {
        // Arrange
        var originalStop = new RedisTrailingStop
        {
            Symbol = "AAPL",
            StopPrice = 150.00m,
            EntryPrice = 160.00m,
            CurrentAtr = 2.50m,
            HighWaterMark = 165.00m,
            Quantity = 100m,
            LastUpdated = DateTime.UtcNow,
            EntryDate = DateTime.UtcNow.AddDays(-5),
            OrderId = "test-order-123"
        };

        // Act
        var json = originalStop.ToJson();
        var deserializedStop = RedisTrailingStop.FromJson(json);

        // Assert
        deserializedStop.Should().NotBeNull();
        deserializedStop!.Symbol.Should().Be(originalStop.Symbol);
        deserializedStop.StopPrice.Should().Be(originalStop.StopPrice);
        deserializedStop.EntryPrice.Should().Be(originalStop.EntryPrice);
        deserializedStop.CurrentAtr.Should().Be(originalStop.CurrentAtr);
        deserializedStop.HighWaterMark.Should().Be(originalStop.HighWaterMark);
        deserializedStop.Quantity.Should().Be(originalStop.Quantity);
        deserializedStop.OrderId.Should().Be(originalStop.OrderId);
    }

    [Fact]
    public void RedisSignalFlag_JsonSerialization_ShouldRoundTrip()
    {
        // Arrange
        var originalFlag = new RedisSignalFlag
        {
            Symbol = "AAPL",
            TradingDate = "2024-06-20",
            SignalTriggered = true,
            TriggeredAt = DateTime.UtcNow,
            SignalStrength = 0.85m,
            Metadata = "test metadata"
        };

        // Act
        var json = originalFlag.ToJson();
        var deserializedFlag = RedisSignalFlag.FromJson(json);

        // Assert
        deserializedFlag.Should().NotBeNull();
        deserializedFlag!.Symbol.Should().Be(originalFlag.Symbol);
        deserializedFlag.TradingDate.Should().Be(originalFlag.TradingDate);
        deserializedFlag.SignalTriggered.Should().Be(originalFlag.SignalTriggered);
        deserializedFlag.SignalStrength.Should().Be(originalFlag.SignalStrength);
        deserializedFlag.Metadata.Should().Be(originalFlag.Metadata);
    }

    private async Task SeedTestDataAsync()
    {
        var testData = new[]
        {
            new TrailingStopRecord
            {
                Symbol = "AAPL",
                Date = DateTime.UtcNow.Date,
                StopPrice = 150.00m,
                EntryPrice = 160.00m,
                Atr = 2.50m,
                HighWaterMark = 165.00m,
                Quantity = 100m,
                EntryDate = DateTime.UtcNow.AddDays(-5),
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            },
            new TrailingStopRecord
            {
                Symbol = "MSFT",
                Date = DateTime.UtcNow.Date,
                StopPrice = 300.00m,
                EntryPrice = 320.00m,
                Atr = 5.00m,
                HighWaterMark = 330.00m,
                Quantity = 50m,
                EntryDate = DateTime.UtcNow.AddDays(-3),
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            }
        };

        _dbContext.TrailingStops.AddRange(testData);
        await _dbContext.SaveChangesAsync();
    }

    public void Dispose()
    {
        _service?.Dispose();
        _dbContext?.Dispose();
    }
}
