using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json;

namespace SmaTrendFollower.Models;

/// <summary>
/// Model for trailing stop-loss data stored in Redis cache.
/// Used for fast retrieval during live trading to maintain stop levels.
/// </summary>
public class RedisTrailingStop
{
    /// <summary>
    /// Stock symbol (e.g., "AAPL", "TSLA")
    /// </summary>
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Current trailing stop price level
    /// </summary>
    public decimal StopPrice { get; set; }

    /// <summary>
    /// Entry price when position was opened
    /// </summary>
    public decimal EntryPrice { get; set; }

    /// <summary>
    /// Current ATR value used for stop calculation
    /// </summary>
    public decimal CurrentAtr { get; set; }

    /// <summary>
    /// Highest price seen since entry (for trailing calculation)
    /// </summary>
    public decimal HighWaterMark { get; set; }

    /// <summary>
    /// Position quantity (positive for long positions)
    /// </summary>
    public decimal Quantity { get; set; }

    /// <summary>
    /// When this stop level was last updated
    /// </summary>
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// Date when position was opened
    /// </summary>
    public DateTime EntryDate { get; set; }

    /// <summary>
    /// Alpaca order ID for the current stop-loss order (if any)
    /// </summary>
    public string? OrderId { get; set; }

    /// <summary>
    /// Serialize to JSON for Redis storage
    /// </summary>
    public string ToJson()
    {
        return JsonSerializer.Serialize(this);
    }

    /// <summary>
    /// Deserialize from JSON stored in Redis
    /// </summary>
    public static RedisTrailingStop? FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return null;

        try
        {
            return JsonSerializer.Deserialize<RedisTrailingStop>(json);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Generate Redis key for this trailing stop
    /// </summary>
    public static string GetRedisKey(string symbol) => $"stop:{symbol}";
}

/// <summary>
/// Model for tracking daily trading signals to prevent duplicate trades.
/// Stored in Redis with daily expiration.
/// </summary>
public class RedisSignalFlag
{
    /// <summary>
    /// Stock symbol (e.g., "AAPL", "TSLA")
    /// </summary>
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Trading date (YYYY-MM-DD format)
    /// </summary>
    public string TradingDate { get; set; } = string.Empty;

    /// <summary>
    /// Whether a signal was triggered for this symbol today
    /// </summary>
    public bool SignalTriggered { get; set; }

    /// <summary>
    /// When the signal was first triggered
    /// </summary>
    public DateTime? TriggeredAt { get; set; }

    /// <summary>
    /// Signal strength or score (optional)
    /// </summary>
    public decimal? SignalStrength { get; set; }

    /// <summary>
    /// Additional metadata about the signal
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Serialize to JSON for Redis storage
    /// </summary>
    public string ToJson()
    {
        return JsonSerializer.Serialize(this);
    }

    /// <summary>
    /// Deserialize from JSON stored in Redis
    /// </summary>
    public static RedisSignalFlag? FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return null;

        try
        {
            return JsonSerializer.Deserialize<RedisSignalFlag>(json);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Generate Redis key for this signal flag
    /// </summary>
    public static string GetRedisKey(string symbol, DateTime date) => $"signal:{symbol}:{date:yyyyMMdd}";

    /// <summary>
    /// Generate Redis key for this signal flag using string date
    /// </summary>
    public static string GetRedisKey(string symbol, string dateString) => $"signal:{symbol}:{dateString}";
}

/// <summary>
/// Model for daily trading throttle/block flags.
/// Used to prevent trading specific symbols on certain days.
/// </summary>
public class RedisThrottleFlag
{
    /// <summary>
    /// Stock symbol (e.g., "AAPL", "TSLA")
    /// </summary>
    public string Symbol { get; set; } = string.Empty;

    /// <summary>
    /// Trading date (YYYY-MM-DD format)
    /// </summary>
    public string TradingDate { get; set; } = string.Empty;

    /// <summary>
    /// Whether trading is blocked for this symbol today
    /// </summary>
    public bool IsBlocked { get; set; }

    /// <summary>
    /// Reason for the block (e.g., "earnings", "volatility", "manual")
    /// </summary>
    public string? BlockReason { get; set; }

    /// <summary>
    /// When the block was set
    /// </summary>
    public DateTime BlockedAt { get; set; }

    /// <summary>
    /// Who/what set the block
    /// </summary>
    public string? BlockedBy { get; set; }

    /// <summary>
    /// Serialize to JSON for Redis storage
    /// </summary>
    public string ToJson()
    {
        return JsonSerializer.Serialize(this);
    }

    /// <summary>
    /// Deserialize from JSON stored in Redis
    /// </summary>
    public static RedisThrottleFlag? FromJson(string json)
    {
        if (string.IsNullOrEmpty(json))
            return null;

        try
        {
            return JsonSerializer.Deserialize<RedisThrottleFlag>(json);
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// Generate Redis key for this throttle flag
    /// </summary>
    public static string GetRedisKey(string symbol, DateTime date) => $"block:{symbol}:{date:yyyyMMdd}";

    /// <summary>
    /// Generate Redis key for this throttle flag using string date
    /// </summary>
    public static string GetRedisKey(string symbol, string dateString) => $"block:{symbol}:{dateString}";
}

/// <summary>
/// Configuration for Redis cache warming operations
/// </summary>
public class RedisWarmingConfig
{
    /// <summary>
    /// Essential symbols to always warm (e.g., SPY, QQQ, major positions)
    /// </summary>
    public string[] EssentialSymbols { get; set; } = Array.Empty<string>();

    /// <summary>
    /// Number of days of historical stop data to load
    /// </summary>
    public int HistoricalDays { get; set; } = 7;

    /// <summary>
    /// Whether to warm signal flags for today
    /// </summary>
    public bool WarmSignalFlags { get; set; } = true;

    /// <summary>
    /// Whether to warm throttle flags
    /// </summary>
    public bool WarmThrottleFlags { get; set; } = true;

    /// <summary>
    /// Default TTL for cached items (in hours)
    /// </summary>
    public int DefaultTtlHours { get; set; } = 24;

    /// <summary>
    /// Maximum number of symbols to process concurrently
    /// </summary>
    public int MaxConcurrency { get; set; } = 10;

    /// <summary>
    /// Default configuration for production use
    /// </summary>
    public static RedisWarmingConfig Default => new()
    {
        EssentialSymbols = new[] { "SPY", "QQQ", "IWM", "VIX", "TLT", "GLD", "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA" },
        HistoricalDays = 7,
        WarmSignalFlags = true,
        WarmThrottleFlags = true,
        DefaultTtlHours = 24,
        MaxConcurrency = 10
    };
}
