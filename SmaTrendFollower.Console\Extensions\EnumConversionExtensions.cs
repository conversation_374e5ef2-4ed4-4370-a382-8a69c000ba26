using Alpaca.Markets;
using SmaTrendFollower.Models;
using System.ComponentModel;

namespace SmaTrendFollower.Extensions;

/// <summary>
/// Extension methods for safe enum conversion between SmaTrendFollower enums and external SDK enums.
/// Provides conflict-free mapping between different enum types.
/// </summary>
public static class EnumConversionExtensions
{
    #region Alpaca.Markets Enum Conversions

    /// <summary>
    /// Converts SmaTrendFollower order side to Alpaca order side.
    /// </summary>
    public static OrderSide ToAlpacaOrderSide(this SmaOrderSide smaOrderSide)
    {
        return smaOrderSide switch
        {
            SmaOrderSide.Buy => OrderSide.Buy,
            SmaOrderSide.Sell => OrderSide.Sell,
            _ => throw new ArgumentOutOfRangeException(nameof(smaOrderSide), $"Unknown order side: {smaOrderSide}")
        };
    }

    /// <summary>
    /// Converts Alpaca order side to SmaTrendFollower order side.
    /// </summary>
    public static SmaOrderSide ToSmaOrderSide(this OrderSide alpacaOrderSide)
    {
        return alpacaOrderSide switch
        {
            OrderSide.Buy => SmaOrderSide.Buy,
            OrderSide.Sell => SmaOrderSide.Sell,
            _ => throw new ArgumentOutOfRangeException(nameof(alpacaOrderSide), $"Unknown Alpaca order side: {alpacaOrderSide}")
        };
    }

    /// <summary>
    /// Converts SmaTrendFollower order type to Alpaca order type.
    /// </summary>
    public static OrderType ToAlpacaOrderType(this SmaOrderType smaOrderType)
    {
        return smaOrderType switch
        {
            SmaOrderType.Market => OrderType.Market,
            SmaOrderType.Limit => OrderType.Limit,
            SmaOrderType.Stop => OrderType.Stop,
            SmaOrderType.StopLimit => OrderType.StopLimit,
            SmaOrderType.TrailingStop => OrderType.TrailingStop,
            _ => throw new ArgumentOutOfRangeException(nameof(smaOrderType), $"Unknown order type: {smaOrderType}")
        };
    }

    /// <summary>
    /// Converts Alpaca order type to SmaTrendFollower order type.
    /// </summary>
    public static SmaOrderType ToSmaOrderType(this OrderType alpacaOrderType)
    {
        return alpacaOrderType switch
        {
            OrderType.Market => SmaOrderType.Market,
            OrderType.Limit => SmaOrderType.Limit,
            OrderType.Stop => SmaOrderType.Stop,
            OrderType.StopLimit => SmaOrderType.StopLimit,
            OrderType.TrailingStop => SmaOrderType.TrailingStop,
            _ => throw new ArgumentOutOfRangeException(nameof(alpacaOrderType), $"Unknown Alpaca order type: {alpacaOrderType}")
        };
    }

    /// <summary>
    /// Converts Alpaca order status to a descriptive string.
    /// </summary>
    public static string ToDisplayString(this OrderStatus orderStatus)
    {
        return orderStatus switch
        {
            OrderStatus.New => "New",
            OrderStatus.PartiallyFilled => "Partially Filled",
            OrderStatus.Filled => "Filled",
            OrderStatus.DoneForDay => "Done for Day",
            OrderStatus.Canceled => "Canceled",
            OrderStatus.Expired => "Expired",
            OrderStatus.Replaced => "Replaced",
            OrderStatus.PendingCancel => "Pending Cancel",
            OrderStatus.PendingReplace => "Pending Replace",
            OrderStatus.Accepted => "Accepted",
            OrderStatus.PendingNew => "Pending New",
            OrderStatus.AcceptedForBidding => "Accepted for Bidding",
            OrderStatus.Stopped => "Stopped",
            OrderStatus.Rejected => "Rejected",
            OrderStatus.Suspended => "Suspended",
            OrderStatus.Calculated => "Calculated",
            _ => orderStatus.ToString()
        };
    }

    #endregion

    #region Event Impact and Trading Action Conversions

    /// <summary>
    /// Converts event impact level to position size multiplier.
    /// </summary>
    public static decimal ToPositionSizeMultiplier(this SmaEventImpactLevel impactLevel)
    {
        return impactLevel switch
        {
            SmaEventImpactLevel.Low => 1.0m,
            SmaEventImpactLevel.Medium => 0.75m,
            SmaEventImpactLevel.High => 0.5m,
            SmaEventImpactLevel.Critical => 0.25m,
            SmaEventImpactLevel.Unknown => 0.5m, // Conservative default
            _ => 0.5m
        };
    }

    /// <summary>
    /// Converts trading action to boolean indicating if new positions are allowed.
    /// </summary>
    public static bool AllowsNewPositions(this SmaEventTradingAction action)
    {
        return action switch
        {
            SmaEventTradingAction.ContinueNormal => true,
            SmaEventTradingAction.ReducePositionSize => true,
            SmaEventTradingAction.IncreaseMonitoring => true,
            SmaEventTradingAction.ApplyTighterStops => true,
            SmaEventTradingAction.AvoidNewPositions => false,
            SmaEventTradingAction.CloseExistingPositions => false,
            SmaEventTradingAction.NoAction => true,
            _ => false // Conservative default
        };
    }

    /// <summary>
    /// Converts trading action to stop-loss multiplier adjustment.
    /// </summary>
    public static decimal ToStopLossMultiplier(this SmaEventTradingAction action)
    {
        return action switch
        {
            SmaEventTradingAction.ContinueNormal => 1.0m,
            SmaEventTradingAction.ReducePositionSize => 1.0m,
            SmaEventTradingAction.IncreaseMonitoring => 1.0m,
            SmaEventTradingAction.ApplyTighterStops => 0.75m, // Tighter stops
            SmaEventTradingAction.AvoidNewPositions => 1.0m,
            SmaEventTradingAction.CloseExistingPositions => 0.5m, // Very tight stops
            SmaEventTradingAction.NoAction => 1.0m,
            _ => 0.75m // Conservative default
        };
    }

    #endregion

    #region SEC Filing Type Conversions

    /// <summary>
    /// Converts SEC filing type to impact level.
    /// </summary>
    public static SmaEventImpactLevel ToImpactLevel(this SmaSecFilingType filingType)
    {
        return filingType switch
        {
            SmaSecFilingType.Form10K => SmaEventImpactLevel.Medium,
            SmaSecFilingType.Form10Q => SmaEventImpactLevel.Medium,
            SmaSecFilingType.Form8K => SmaEventImpactLevel.High,
            SmaSecFilingType.ProxyStatement => SmaEventImpactLevel.Low,
            SmaSecFilingType.Form13F => SmaEventImpactLevel.Low,
            SmaSecFilingType.Form4 => SmaEventImpactLevel.Medium,
            SmaSecFilingType.Form3 => SmaEventImpactLevel.Low,
            SmaSecFilingType.Form5 => SmaEventImpactLevel.Low,
            SmaSecFilingType.FormS1 => SmaEventImpactLevel.High,
            SmaSecFilingType.Form424B => SmaEventImpactLevel.Medium,
            SmaSecFilingType.Unknown => SmaEventImpactLevel.Unknown,
            _ => SmaEventImpactLevel.Unknown
        };
    }

    /// <summary>
    /// Determines if a SEC filing type requires immediate attention.
    /// </summary>
    public static bool RequiresImmediateAttention(this SmaSecFilingType filingType)
    {
        return filingType switch
        {
            SmaSecFilingType.Form8K => true,
            SmaSecFilingType.FormS1 => true,
            SmaSecFilingType.Form4 => true,
            _ => false
        };
    }

    #endregion

    #region Market Event Type Conversions

    /// <summary>
    /// Converts market event type to impact level.
    /// </summary>
    public static SmaEventImpactLevel ToImpactLevel(this SmaMarketEventType eventType)
    {
        return eventType switch
        {
            SmaMarketEventType.EarningsAnnouncement => SmaEventImpactLevel.High,
            SmaMarketEventType.DividendDeclaration => SmaEventImpactLevel.Low,
            SmaMarketEventType.StockSplit => SmaEventImpactLevel.Medium,
            SmaMarketEventType.MergerAcquisition => SmaEventImpactLevel.Critical,
            SmaMarketEventType.Spinoff => SmaEventImpactLevel.High,
            SmaMarketEventType.IpoLaunch => SmaEventImpactLevel.High,
            SmaMarketEventType.DelistingNotice => SmaEventImpactLevel.Critical,
            SmaMarketEventType.BankruptcyFiling => SmaEventImpactLevel.Critical,
            SmaMarketEventType.FdaApproval => SmaEventImpactLevel.Critical,
            SmaMarketEventType.ProductLaunch => SmaEventImpactLevel.Medium,
            SmaMarketEventType.ConferenceCall => SmaEventImpactLevel.Low,
            SmaMarketEventType.AnalystRatingChange => SmaEventImpactLevel.Medium,
            SmaMarketEventType.InsiderTrading => SmaEventImpactLevel.Medium,
            SmaMarketEventType.ShareBuyback => SmaEventImpactLevel.Medium,
            SmaMarketEventType.GuidanceUpdate => SmaEventImpactLevel.High,
            SmaMarketEventType.Unknown => SmaEventImpactLevel.Unknown,
            _ => SmaEventImpactLevel.Unknown
        };
    }

    /// <summary>
    /// Determines if a market event type typically causes high volatility.
    /// </summary>
    public static bool CausesHighVolatility(this SmaMarketEventType eventType)
    {
        return eventType switch
        {
            SmaMarketEventType.EarningsAnnouncement => true,
            SmaMarketEventType.MergerAcquisition => true,
            SmaMarketEventType.FdaApproval => true,
            SmaMarketEventType.BankruptcyFiling => true,
            SmaMarketEventType.DelistingNotice => true,
            SmaMarketEventType.GuidanceUpdate => true,
            _ => false
        };
    }

    #endregion

    #region Time Period Conversions

    /// <summary>
    /// Converts event time period to TimeSpan.
    /// </summary>
    public static TimeSpan ToTimeSpan(this SmaEventTimePeriod timePeriod)
    {
        return timePeriod switch
        {
            SmaEventTimePeriod.Next24Hours => TimeSpan.FromDays(1),
            SmaEventTimePeriod.Next3Days => TimeSpan.FromDays(3),
            SmaEventTimePeriod.NextWeek => TimeSpan.FromDays(7),
            SmaEventTimePeriod.NextMonth => TimeSpan.FromDays(30),
            SmaEventTimePeriod.Past24Hours => TimeSpan.FromDays(-1),
            SmaEventTimePeriod.Past3Days => TimeSpan.FromDays(-3),
            SmaEventTimePeriod.PastWeek => TimeSpan.FromDays(-7),
            SmaEventTimePeriod.PastMonth => TimeSpan.FromDays(-30),
            SmaEventTimePeriod.CustomRange => TimeSpan.Zero,
            _ => TimeSpan.Zero
        };
    }

    /// <summary>
    /// Gets the start and end dates for an event time period.
    /// </summary>
    public static (DateTime Start, DateTime End) ToDateRange(this SmaEventTimePeriod timePeriod, DateTime? referenceDate = null)
    {
        var reference = referenceDate ?? DateTime.UtcNow;
        var timeSpan = timePeriod.ToTimeSpan();

        if (timeSpan.TotalDays > 0)
        {
            // Future periods
            return (reference, reference.Add(timeSpan));
        }
        else if (timeSpan.TotalDays < 0)
        {
            // Past periods
            return (reference.Add(timeSpan), reference);
        }
        else
        {
            // Custom or zero
            return (reference, reference);
        }
    }

    #endregion

    #region Validation Extensions

    /// <summary>
    /// Validates if an enum value is defined.
    /// </summary>
    public static bool IsValidEnumValue<T>(this T enumValue) where T : Enum
    {
        return Enum.IsDefined(typeof(T), enumValue);
    }

    /// <summary>
    /// Gets a safe enum value, returning a default if the value is not defined.
    /// </summary>
    public static T GetSafeEnumValue<T>(this T enumValue, T defaultValue) where T : Enum
    {
        return enumValue.IsValidEnumValue() ? enumValue : defaultValue;
    }

    #endregion
}
